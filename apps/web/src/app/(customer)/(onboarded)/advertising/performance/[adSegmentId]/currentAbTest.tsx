import { api } from "@/trpc/client";
import { Award, Calendar, Eye, TrendingUp } from "lucide-react";

import { Badge } from "@kalos/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@kalos/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@kalos/ui/table/table";

import { AbTestType } from "../../../../../../../../../backend/src/modules/advertising/abTest/internal/domain/abTestType.valueObject";

export function CurrentAbTest({
  abTestId,
  abTestType,
  adSegmentId,
}: {
  abTestId: string;
  abTestType: AbTestType;
  adSegmentId: string;
}) {
  const abTestQuery = api.v2.ads.newAbTestController.getAbTestData.useQuery({
    abTestId: abTestId,
    type: abTestType,
    adSegmentId: adSegmentId,
  });

  // Debug logging
  console.log("AB Test Query Debug:", {
    abTestId,
    abTestType,
    adSegmentId,
    isLoading: abTestQuery.isLoading,
    isError: abTestQuery.isError,
    error: abTestQuery.error,
    data: abTestQuery.data,
  });

  // Get current round data with metrics
  const currentRoundDataQuery =
    api.v2.ads.newAbTestController.getAbTestRoundData.useQuery(
      {
        abTestRoundId: abTestQuery.data?.currentRound?.id || "",
        adSegmentId: adSegmentId,
        type: abTestType,
      },
      {
        enabled: !!abTestQuery.data?.currentRound?.id,
      },
    );

  // Get past rounds data from abTestData
  const pastRoundsData = abTestQuery.data?.pastRounds || [];

  // Get upcoming rounds data from abTestData
  const upcomingRoundsData = abTestQuery.data?.upcomingRounds || [];

  if (abTestQuery.isLoading || currentRoundDataQuery.isLoading) {
    return <div>Loading AB test data...</div>;
  }

  if (abTestQuery.isError || currentRoundDataQuery.isError) {
    return <div>Error loading AB test data</div>;
  }

  if (!abTestQuery.data) {
    return (
      <div>
        <div>No AB test data available</div>
        <div className="mt-2 text-sm text-gray-500">
          Debug: abTestId={abTestId}, type={abTestType}, adSegmentId=
          {adSegmentId}
        </div>
        {abTestQuery.error && (
          <div className="mt-2 text-sm text-red-500">
            Error: {JSON.stringify(abTestQuery.error)}
          </div>
        )}
      </div>
    );
  }

  const roundData = currentRoundDataQuery.data;
  const hasCurrentRound = !!abTestQuery.data.currentRound && !!roundData;

  // Helper function to format numbers
  const formatNumber = (num: number | null) => {
    if (num === null || num === undefined) return "-";
    return num.toLocaleString();
  };

  // Helper function to format currency
  const formatCurrency = (num: number | null) => {
    if (num === null || num === undefined) return "-";
    return `$${num.toFixed(2)}`;
  };

  // Helper function to calculate CTR
  const calculateCTR = (clicks: number | null, impressions: number | null) => {
    if (!clicks || !impressions || impressions === 0) return "-";
    return `${((clicks / impressions) * 100).toFixed(2)}%`;
  };

  // Helper function to calculate cost per click
  const calculateCostPerClick = (
    cost: number | null,
    clicks: number | null,
  ) => {
    if (!cost || !clicks || clicks === 0) return "-";
    return formatCurrency(cost / clicks);
  };

  // Helper function to get ad title based on variant type and details
  const getAdTitle = (
    variant:
      | NonNullable<typeof roundData>["currentBest"]
      | NonNullable<typeof roundData>["contender"],
    role: "Current Best" | "Contender",
  ) => {
    const variantDetails = variant.varientsUsedInSponsoredCreative;

    if (variantDetails.adFormatType === "SPONSORED_CONTENT") {
      return `${variantDetails.creativeType}`;
    } else if (variantDetails.adFormatType === "SPONSORED_INMAIL") {
      return `${variantDetails.conversationSubjectCopyType}`;
    }

    return `Ad ${variant.sponsoredCreativeId.slice(-8)}`;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">
              Learning Goal: {abTestType}
            </CardTitle>
            <CardDescription>
              Current AB Test Round{" "}
              {abTestQuery.data.currentRound?.roundIndex ?? 0 + 1}
            </CardDescription>
          </div>
          <Badge variant="secondary" className="bg-blue-50 text-blue-700">
            <Calendar className="mr-1 h-3 w-3" />
            Active
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">Ad Title</TableHead>
                <TableHead>Cost/Key Result</TableHead>
                <TableHead>Key Result</TableHead>
                <TableHead>Timeline</TableHead>
                <TableHead className="text-center">#Demos</TableHead>
                <TableHead className="text-center">#Closed-Won</TableHead>
                <TableHead>Total Spent</TableHead>
                <TableHead className="text-center">#Clicks</TableHead>
                <TableHead className="text-center">CTR</TableHead>
                <TableHead>Cost/Click</TableHead>
                <TableHead className="text-center">#Impressions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {/* Past Ads Section */}
              <TableRow className="bg-blue-50">
                <TableCell
                  colSpan={11}
                  className="py-3 font-medium text-blue-900"
                >
                  <div className="flex items-center space-x-2">
                    <Award className="h-4 w-4" />
                    <span>Past Ads</span>
                  </div>
                </TableCell>
              </TableRow>
              {pastRoundsData.map((round, index) => (
                <TableRow
                  key={`past-${round.id}-${index}`}
                  className="hover:bg-gray-50"
                >
                  <TableCell className="font-medium">
                    <div className="flex items-center space-x-2">
                      <div className="flex h-8 w-8 items-center justify-center rounded border bg-gray-100">
                        <Eye className="h-4 w-4 text-gray-500" />
                      </div>
                      <span>
                        Round {round.roundIndex + 1} -{" "}
                        {round.winner === "CURRENT_BEST"
                          ? "Winner"
                          : "Challenger"}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">-</TableCell>
                  <TableCell>-</TableCell>
                  <TableCell className="text-sm text-gray-600">
                    Completed
                  </TableCell>
                  <TableCell className="text-center">-</TableCell>
                  <TableCell className="text-center font-medium">-</TableCell>
                  <TableCell className="font-medium">-</TableCell>
                  <TableCell className="text-center text-gray-400">-</TableCell>
                  <TableCell className="text-center text-gray-400">-</TableCell>
                  <TableCell className="text-gray-400">-</TableCell>
                  <TableCell className="text-center text-gray-400">-</TableCell>
                </TableRow>
              ))}

              {/* Current Ads Section */}
              <TableRow className="bg-green-50">
                <TableCell
                  colSpan={11}
                  className="py-3 font-medium text-green-900"
                >
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-4 w-4" />
                    <span>Current Ads</span>
                  </div>
                </TableCell>
              </TableRow>
              {hasCurrentRound ? (
                <>
                  {/* Current Best */}
                  <TableRow className="hover:bg-gray-50">
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-2">
                        <div className="flex h-8 w-8 items-center justify-center rounded border bg-gray-100">
                          <Eye className="h-4 w-4 text-gray-500" />
                        </div>
                        <span>
                          {getAdTitle(roundData!.currentBest, "Current Best")}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">
                      {roundData!.currentBest.metrics.leads &&
                      roundData!.currentBest.metrics.cost
                        ? formatCurrency(
                            roundData!.currentBest.metrics.cost /
                              roundData!.currentBest.metrics.leads,
                          )
                        : "-"}
                    </TableCell>
                    <TableCell>
                      {formatNumber(roundData!.currentBest.metrics.leads)}
                    </TableCell>
                    <TableCell className="text-sm text-gray-600">
                      Active
                    </TableCell>
                    <TableCell className="text-center">
                      {formatNumber(roundData!.currentBest.metrics.conversions)}
                    </TableCell>
                    <TableCell className="text-center font-medium">
                      {formatNumber(roundData!.currentBest.metrics.leads)}
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(roundData!.currentBest.metrics.cost)}
                    </TableCell>
                    <TableCell className="text-center text-gray-400">
                      {formatNumber(roundData!.currentBest.metrics.clicks)}
                    </TableCell>
                    <TableCell className="text-center text-gray-400">
                      {calculateCTR(
                        roundData!.currentBest.metrics.clicks,
                        roundData!.currentBest.metrics.impressions,
                      )}
                    </TableCell>
                    <TableCell className="text-gray-400">
                      {calculateCostPerClick(
                        roundData!.currentBest.metrics.cost,
                        roundData!.currentBest.metrics.clicks,
                      )}
                    </TableCell>
                    <TableCell className="text-center text-gray-400">
                      {formatNumber(roundData!.currentBest.metrics.impressions)}
                    </TableCell>
                  </TableRow>
                  {/* Contender */}
                  <TableRow className="hover:bg-gray-50">
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-2">
                        <div className="flex h-8 w-8 items-center justify-center rounded border bg-gray-100">
                          <Eye className="h-4 w-4 text-gray-500" />
                        </div>
                        <span>
                          {getAdTitle(roundData!.contender, "Contender")}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">
                      {roundData!.contender.metrics.leads &&
                      roundData!.contender.metrics.cost
                        ? formatCurrency(
                            roundData!.contender.metrics.cost /
                              roundData!.contender.metrics.leads,
                          )
                        : "-"}
                    </TableCell>
                    <TableCell>
                      {formatNumber(roundData!.contender.metrics.leads)}
                    </TableCell>
                    <TableCell className="text-sm text-gray-600">
                      Active
                    </TableCell>
                    <TableCell className="text-center">
                      {formatNumber(roundData!.contender.metrics.conversions)}
                    </TableCell>
                    <TableCell className="text-center font-medium">
                      {formatNumber(roundData!.contender.metrics.leads)}
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(roundData!.contender.metrics.cost)}
                    </TableCell>
                    <TableCell className="text-center text-gray-400">
                      {formatNumber(roundData!.contender.metrics.clicks)}
                    </TableCell>
                    <TableCell className="text-center text-gray-400">
                      {calculateCTR(
                        roundData!.contender.metrics.clicks,
                        roundData!.contender.metrics.impressions,
                      )}
                    </TableCell>
                    <TableCell className="text-gray-400">
                      {calculateCostPerClick(
                        roundData!.contender.metrics.cost,
                        roundData!.contender.metrics.clicks,
                      )}
                    </TableCell>
                    <TableCell className="text-center text-gray-400">
                      {formatNumber(roundData!.contender.metrics.impressions)}
                    </TableCell>
                  </TableRow>
                </>
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={11}
                    className="py-4 text-center text-gray-500"
                  >
                    No current ads running
                  </TableCell>
                </TableRow>
              )}

              {/* Upcoming Ads Section */}
              <TableRow className="bg-orange-50">
                <TableCell
                  colSpan={11}
                  className="py-3 font-medium text-orange-900"
                >
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4" />
                    <span>Upcoming Ads</span>
                  </div>
                </TableCell>
              </TableRow>
              {upcomingRoundsData.map((round, index) => (
                <TableRow
                  key={`upcoming-${round.id}-${index}`}
                  className="hover:bg-gray-50"
                >
                  <TableCell className="font-medium">
                    <div className="flex items-center space-x-2">
                      <div className="flex h-8 w-8 items-center justify-center rounded border bg-gray-100">
                        <Eye className="h-4 w-4 text-gray-500" />
                      </div>
                      <span>Round {round.roundIndex + 1} - Scheduled</span>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">-</TableCell>
                  <TableCell>-</TableCell>
                  <TableCell className="text-sm text-gray-600">
                    Upcoming
                  </TableCell>
                  <TableCell className="text-center">-</TableCell>
                  <TableCell className="text-center font-medium">-</TableCell>
                  <TableCell className="font-medium">-</TableCell>
                  <TableCell className="text-center text-gray-400">-</TableCell>
                  <TableCell className="text-center text-gray-400">-</TableCell>
                  <TableCell className="text-gray-400">-</TableCell>
                  <TableCell className="text-center text-gray-400">-</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
