import { api } from "@/trpc/client";

import { AbTestType } from "../../../../../../../../../backend/src/modules/advertising/abTest/internal/domain/abTestType.valueObject";

export function CurrentAbTest({
  abTestId,
  abTestType,
  adSegmentId,
}: {
  abTestId: string;
  abTestType: AbTestType;
  adSegmentId: string;
}) {
  const abTestQuery = api.v2.ads.newAbTestController.getAbTestData.useQuery({
    abTestId: abTestId,
    type: abTestType,
    adSegmentId: adSegmentId,
  });

  return <div>Current Ab Test</div>;
}
